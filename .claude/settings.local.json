{"permissions": {"allow": ["Bash(rg:*)", "Bash(rg:*)", "Bash(grep:*)", "mcp__cw-mcp-server__list_log_groups", "Bash(node:*)", "Bash(rush build)", "Bash(nvm use:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(rushx build:*)", "Bash(rushx --to portal-api lint)", "Bash(./src/core-agent/testing/run-processor-test-with-logs.sh:*)", "Bash(./src/core-agent/testing/run-e2e-with-logs.sh:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(chmod:*)", "Bash(./src/core-agent/testing/run-rush-processing-tests.sh:*)", "<PERSON><PERSON>(timeout:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:github.com)", "Bash(./pr-actionable-comments.sh:*)", "<PERSON><PERSON>(jq:*)", "mcp__ide__executeCode", "<PERSON><PERSON>(echo:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(APIFY_TOKEN=********************************************** node src/us-tariff/testing/test-kv-store-direct.js)", "Bash(APIFY_TOKEN=********************************************** node src/us-tariff/testing/test-apify-access.js)", "Bash(rush build:*)", "Bash(npx typeorm migration:generate:*)", "Bash(npx typeorm migration:run:*)"], "deny": []}}