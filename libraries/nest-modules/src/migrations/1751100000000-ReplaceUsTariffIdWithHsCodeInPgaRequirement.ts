import { MigrationInterface, QueryRunner } from "typeorm";

export class ReplaceUsTariffIdWithHsCodeInPgaRequirement1751100000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Add hsCode column to us_tariff_pga_requirement table
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD COLUMN "hsCode" varchar(10)
    `);

    // Step 2: Populate hsCode from existing tariff relationships
    await queryRunner.query(`
      UPDATE "us_tariff_pga_requirement" utpr
      SET "hsCode" = ut."hsCode"
      FROM "us_tariff" ut
      WHERE utpr."usTariffId" = ut.id
    `);

    // Step 3: Make hsCode NOT NULL after population
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ALTER COLUMN "hsCode" SET NOT NULL
    `);

    // Step 4: Add check constraint for hsCode format (10 characters)
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "CHK_US_TARIFF_PGA_HS_CODE_LENGTH" CHECK (char_length("hsCode") = 10)
    `);

    // Step 5: Create new index on hsCode
    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_HS_CODE" ON "us_tariff_pga_requirement" ("hsCode")
    `);

    // Step 6: Drop old foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP CONSTRAINT "FK_55dd99c5c9aac53214a7e4bfb13"
    `);

    // Step 7: Drop old index
    await queryRunner.query(`
      DROP INDEX "IDX_US_TARIFF_PGA_TARIFF_ID"
    `);

    // Step 8: Update unique constraint to use hsCode instead of usTariffId
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP CONSTRAINT "UQ_us_tariff_pga_requirement"
    `);

    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "UQ_us_tariff_pga_requirement" UNIQUE ("hsCode", "usPgaRequirementId")
    `);

    // Step 9: Drop usTariffId column
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP COLUMN "usTariffId"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Reverse the migration

    // Step 1: Add back usTariffId column
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD COLUMN "usTariffId" integer
    `);

    // Step 2: Populate usTariffId from hsCode relationships
    await queryRunner.query(`
      UPDATE "us_tariff_pga_requirement" utpr
      SET "usTariffId" = ut.id
      FROM "us_tariff" ut
      WHERE utpr."hsCode" = ut."hsCode"
    `);

    // Step 3: Make usTariffId NOT NULL
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ALTER COLUMN "usTariffId" SET NOT NULL
    `);

    // Step 4: Restore foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "FK_55dd99c5c9aac53214a7e4bfb13"
      FOREIGN KEY ("usTariffId") REFERENCES "us_tariff"("id") ON DELETE CASCADE
    `);

    // Step 5: Restore old index
    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_TARIFF_ID" ON "us_tariff_pga_requirement" ("usTariffId")
    `);

    // Step 6: Update unique constraint back to usTariffId
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP CONSTRAINT "UQ_us_tariff_pga_requirement"
    `);

    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "UQ_us_tariff_pga_requirement" UNIQUE ("usTariffId", "usPgaRequirementId")
    `);

    // Step 7: Drop hsCode constraints and index
    await queryRunner.query(`
      DROP INDEX "IDX_US_TARIFF_PGA_HS_CODE"
    `);

    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP CONSTRAINT "CHK_US_TARIFF_PGA_HS_CODE_LENGTH"
    `);

    // Step 8: Drop hsCode column
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP COLUMN "hsCode"
    `);
  }
}
