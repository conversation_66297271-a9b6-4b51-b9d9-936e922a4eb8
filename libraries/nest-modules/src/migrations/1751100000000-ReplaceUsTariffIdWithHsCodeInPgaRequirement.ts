import { MigrationInterface, QueryRunner } from "typeorm";

export class ReplaceUsTariffIdWithHsCodeInPgaRequirement1751100000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Since these tables haven't been pushed to production yet, we can safely drop and recreate

    // Step 1: Drop the existing table
    await queryRunner.query(`DROP TABLE IF EXISTS "us_tariff_pga_requirement"`);

    // Step 2: Create the new table with the correct structure
    await queryRunner.query(`
      CREATE TABLE "us_tariff_pga_requirement" (
        "id" SERIAL NOT NULL,
        "createDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "lastEditDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "hsCode" varchar(10) NOT NULL,
        "tariffFlagCode" varchar(3) NOT NULL,
        "notes" text,
        "usPgaRequirementId" integer NOT NULL,
        CONSTRAINT "PK_us_tariff_pga_requirement" PRIMARY KEY ("id")
      )
    `);

    // Step 3: Add check constraint for hsCode format (10 characters)
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "CHK_US_TARIFF_PGA_HS_CODE_LENGTH" CHECK (char_length("hsCode") = 10)
    `);

    // Step 4: Create indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_HS_CODE" ON "us_tariff_pga_requirement" ("hsCode")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_TARIFF_FLAG_CODE" ON "us_tariff_pga_requirement" ("tariffFlagCode")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_REQUIREMENT_ID" ON "us_tariff_pga_requirement" ("usPgaRequirementId")
    `);

    // Step 5: Add unique constraint on hsCode and tariffFlagCode combination
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "UQ_US_TARIFF_PGA_HS_CODE_TARIFF_FLAG_CODE" UNIQUE ("hsCode", "tariffFlagCode")
    `);

    // Step 6: Add foreign key constraint to us_pga_requirement
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "FK_us_tariff_pga_requirement_usPgaRequirement"
      FOREIGN KEY ("usPgaRequirementId") REFERENCES "us_pga_requirement"("id") ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Since we're dropping and recreating, the down migration should restore the original structure

    // Step 1: Drop the new table
    await queryRunner.query(`DROP TABLE IF EXISTS "us_tariff_pga_requirement"`);

    // Step 2: Recreate the original table structure (with usTariffId foreign key)
    await queryRunner.query(`
      CREATE TABLE "us_tariff_pga_requirement" (
        "id" SERIAL NOT NULL,
        "createDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "lastEditDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "usTariffId" integer NOT NULL,
        "usPgaRequirementId" integer NOT NULL,
        "notes" text,
        CONSTRAINT "PK_us_tariff_pga_requirement" PRIMARY KEY ("id")
      )
    `);

    // Step 3: Create original indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_TARIFF_ID" ON "us_tariff_pga_requirement" ("usTariffId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_REQUIREMENT_ID" ON "us_tariff_pga_requirement" ("usPgaRequirementId")
    `);

    // Step 4: Add original foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "FK_55dd99c5c9aac53214a7e4bfb13"
      FOREIGN KEY ("usTariffId") REFERENCES "us_tariff"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "FK_ba2f7649035ce383625b9b350fe"
      FOREIGN KEY ("usPgaRequirementId") REFERENCES "us_pga_requirement"("id") ON DELETE CASCADE
    `);
  }
}
