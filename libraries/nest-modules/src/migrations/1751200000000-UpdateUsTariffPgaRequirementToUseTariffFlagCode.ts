import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateUsTariffPgaRequirementToUseTariffFlagCode1751200000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Add tariffFlagCode column to us_tariff_pga_requirement table
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD COLUMN "tariffFlagCode" varchar(3)
    `);

    // Step 2: Populate tariffFlagCode from existing usPgaRequirementId relationships
    await queryRunner.query(`
      UPDATE "us_tariff_pga_requirement" utpr
      SET "tariffFlagCode" = upr."tariffFlagCode"
      FROM "us_pga_requirement" upr
      WHERE utpr."usPgaRequirementId" = upr.id
    `);

    // Step 3: Make tariffFlagCode NOT NULL after population
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ALTER COLUMN "tariffFlagCode" SET NOT NULL
    `);

    // Step 4: Create new index on tariffFlagCode
    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_TARIFF_FLAG_CODE" ON "us_tariff_pga_requirement" ("tariffFlagCode")
    `);

    // Step 5: Add new unique constraint using hsCode and tariffFlagCode
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "UQ_US_TARIFF_PGA_HS_CODE_TARIFF_FLAG_CODE" UNIQUE ("hsCode", "tariffFlagCode")
    `);

    // Step 6: Drop old unique constraint
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP CONSTRAINT "UQ_us_tariff_pga_requirement"
    `);

    // Step 7: Drop old foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP CONSTRAINT "FK_us_tariff_pga_requirement_usPgaRequirement"
    `);

    // Step 8: Drop old index
    await queryRunner.query(`
      DROP INDEX "IDX_US_TARIFF_PGA_REQUIREMENT_ID"
    `);

    // Step 9: Drop usPgaRequirementId column
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP COLUMN "usPgaRequirementId"
    `);

    // Step 10: Add index to us_pga_requirement.tariffFlagCode for performance
    await queryRunner.query(`
      CREATE INDEX "IDX_US_PGA_REQUIREMENT_TARIFF_FLAG_CODE" ON "us_pga_requirement" ("tariffFlagCode")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Reverse the migration

    // Step 1: Add back usPgaRequirementId column
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD COLUMN "usPgaRequirementId" integer
    `);

    // Step 2: Populate usPgaRequirementId from tariffFlagCode relationships
    await queryRunner.query(`
      UPDATE "us_tariff_pga_requirement" utpr
      SET "usPgaRequirementId" = upr.id
      FROM "us_pga_requirement" upr
      WHERE utpr."tariffFlagCode" = upr."tariffFlagCode"
    `);

    // Step 3: Make usPgaRequirementId NOT NULL
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ALTER COLUMN "usPgaRequirementId" SET NOT NULL
    `);

    // Step 4: Restore foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "FK_us_tariff_pga_requirement_usPgaRequirement" 
      FOREIGN KEY ("usPgaRequirementId") REFERENCES "us_pga_requirement"("id") ON DELETE CASCADE
    `);

    // Step 5: Restore old index
    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_REQUIREMENT_ID" ON "us_tariff_pga_requirement" ("usPgaRequirementId")
    `);

    // Step 6: Restore old unique constraint
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      ADD CONSTRAINT "UQ_us_tariff_pga_requirement" UNIQUE ("hsCode", "usPgaRequirementId")
    `);

    // Step 7: Drop new unique constraint
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP CONSTRAINT "UQ_US_TARIFF_PGA_HS_CODE_TARIFF_FLAG_CODE"
    `);

    // Step 8: Drop tariffFlagCode constraints and index
    await queryRunner.query(`
      DROP INDEX "IDX_US_TARIFF_PGA_TARIFF_FLAG_CODE"
    `);

    // Step 9: Drop tariffFlagCode column
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement"
      DROP COLUMN "tariffFlagCode"
    `);

    // Step 10: Drop us_pga_requirement index
    await queryRunner.query(`
      DROP INDEX "IDX_US_PGA_REQUIREMENT_TARIFF_FLAG_CODE"
    `);
  }
}