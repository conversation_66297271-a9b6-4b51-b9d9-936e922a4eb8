import { MigrationInterface, QueryRunner } from "typeorm";

export class SeedUsTariffPgaRequirementPivotTable1751000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Create the pivot table structure only
    await queryRunner.query(`
      CREATE TABLE "us_tariff_pga_requirement" (
        "id" SERIAL NOT NULL,
        "createDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "lastEditDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "usTariffId" integer NOT NULL,
        "usPgaRequirementId" integer NOT NULL,
        "notes" text,
        CONSTRAINT "PK_us_tariff_pga_requirement" PRIMARY KEY ("id"),
        CONSTRAINT "FK_us_tariff_pga_requirement_usTariff" FOREIGN KEY ("usTariffId") REFERENCES "us_tariff"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_us_tariff_pga_requirement_usPgaRequirement" FOREIGN KEY ("usPgaRequirementId") REFERENCES "us_pga_requirement"("id") ON DELETE CASCADE
      )
    `);

    // Create indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_TARIFF_ID" ON "us_tariff_pga_requirement" ("usTariffId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_REQUIREMENT_ID" ON "us_tariff_pga_requirement" ("usPgaRequirementId")
    `);

    // Create unique constraint to prevent duplicates
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement" 
      ADD CONSTRAINT "UQ_us_tariff_pga_requirement" UNIQUE ("usTariffId", "usPgaRequirementId")
    `);

    // Step 2: Seed the pivot table with existing data (one-time migration)
    // This will be replaced by the 3-step ingestion process for future updates
    await queryRunner.query(`
      INSERT INTO "us_tariff_pga_requirement" ("usTariffId", "usPgaRequirementId", "createDate", "lastEditDate")
      SELECT 
        ut.id as "usTariffId",
        pr.id as "usPgaRequirementId",
        NOW() as "createDate",
        NOW() as "lastEditDate"
      FROM "us_tariff" ut
      CROSS JOIN unnest(ut."pgaCodes") AS pga_code
      INNER JOIN "us_pga_requirement" pr ON pr."tariffFlagCode" = pga_code
      WHERE ut."pgaCodes" IS NOT NULL AND array_length(ut."pgaCodes", 1) > 0
      ON CONFLICT ("usTariffId", "usPgaRequirementId") DO NOTHING
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the pivot table
    await queryRunner.query(`DROP TABLE IF EXISTS "us_tariff_pga_requirement"`);
  }
}
