import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateUsTariffTable1750800000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create us_tariff table
    await queryRunner.query(`
      CREATE TABLE "us_tariff" (
        "id" SERIAL NOT NULL,
        "createDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "lastEditDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "hsCode" character varying(10) NOT NULL,
        "effectiveDate" date NOT NULL,
        "expiryDate" date NOT NULL,
        "description" text NOT NULL,
        "censusQty1" character varying,
        "censusQty2" character varying,
        "censusQty3" character varying,
        "adDuty" boolean,
        "cvDuty" boolean,
        "pgaCodes" text array,
        "createdById" integer,
        "lastEditedById" integer,
        CONSTRAINT "PK_us_tariff" PRIMARY KEY ("id"),
        CONSTRAINT "VALID_HTS_CODE" CHECK (char_length("hsCode") = 10),
        CONSTRAINT "UNIQUE_US_HTS_CODE" UNIQUE ("hsCode"),
        CONSTRAINT "FK_us_tariff_createdBy" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE SET NULL,
        CONSTRAINT "FK_us_tariff_lastEditedBy" FOREIGN KEY ("lastEditedById") REFERENCES "user"("id") ON DELETE SET NULL
      )
    `);

    // Create indexes for us_tariff table
    await queryRunner.query(`
      CREATE INDEX "IDX_us_tariff_hsCode" ON "us_tariff" ("hsCode")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_us_tariff_dates" ON "us_tariff" ("effectiveDate", "expiryDate")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_us_tariff_description" ON "us_tariff" USING gin(to_tsvector('english', "description"))
    `);

    // Note: pga_agency table creation removed as it's not needed
    // us_pga_requirement table contains all necessary agency information

    // Note: Foreign key constraint to commercial_invoice_line removed
    // to keep US Tariff pipeline scope focused. This can be added
    // in a separate migration when the invoice-tariff integration is ready.
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: No foreign key constraint to commercial_invoice_line to drop
    // since it was removed to keep US Tariff pipeline scope focused.

    // Drop us_tariff indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_us_tariff_description"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_us_tariff_dates"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_us_tariff_hsCode"`);

    // Drop us_tariff table
    await queryRunner.query(`DROP TABLE IF EXISTS "us_tariff"`);
  }
}
