import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, Repository } from "typeorm";
import * as XLSX from "xlsx";
import { TariffSyncHistory, UsTariff, UsPgaRequirement, UsTariffPgaRequirement } from "../entities";
import { SyncStatus } from "../types";
import { HTSPGAFileRow, ProcessedTariffData, TariffImportStats } from "../types/us-tariff.types";

@Injectable()
export class UsTariffIngestionService {
  private readonly logger = new Logger(UsTariffIngestionService.name);

  constructor(
    private readonly dataSource: DataSource,
    @InjectRepository(TariffSyncHistory)
    private readonly historyRepository: Repository<TariffSyncHistory>,
    @InjectRepository(UsTariff)
    private readonly usTariffRepository: Repository<UsTariff>,
    @InjectRepository(UsPgaRequirement)
    private readonly usPgaRequirementRepository: Repository<UsPgaRequirement>,
    @InjectRepository(UsTariffPgaRequirement)
    private readonly usTariffPgaRequirementRepository: Repository<UsTariffPgaRequirement>
  ) {}

  /**
   * Ingest US tariff data from XLSX file buffer
   */
  async ingestFromXlsx(fileBuffer: Buffer, initiatedBy: string): Promise<TariffSyncHistory> {
    // Create sync history record
    const history = this.historyRepository.create({
      status: SyncStatus.RUNNING
    });
    await this.historyRepository.save(history);

    try {
      this.logger.log(`Starting US tariff ingestion (3-step process), sync ID: ${history.id}`);

      // Step 1: Parse XLSX file (Apify to XLSX equivalent)
      const rows = await this.parseWorkbook(fileBuffer);
      this.logger.log(`Step 1 completed: Parsed ${rows.length} rows from XLSX file`);

      // Step 2: Load up the DB (main tariff data)
      const stats = await this.upsertRows(rows);
      this.logger.log(`Step 2 completed: Upserted tariff data - ${JSON.stringify(stats)}`);

      // Step 3: Populate pivot table (tariff-PGA relationships)
      const pivotStats = await this.upsertPivotTable(rows);
      this.logger.log(`Step 3 completed: Populated pivot table - ${JSON.stringify(pivotStats)}`);

      // Update history with success
      Object.assign(history, {
        finishDate: new Date(),
        status: SyncStatus.SUCCESS
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Ingestion failed: ${errorMessage}`, errorStack);

      // Update history with failure
      Object.assign(history, {
        finishDate: new Date(),
        status: SyncStatus.FAILED,
        errorMessage
      });

      throw error;
    } finally {
      await this.historyRepository.save(history);
    }

    return history;
  }

  /**
   * Parse XLSX workbook from buffer and extract tariff data
   */
  private async parseWorkbook(fileBuffer: Buffer): Promise<ProcessedTariffData[]> {
    try {
      // Parse XLSX file using SheetJS
      const workbook = XLSX.read(fileBuffer, { type: "buffer" });

      if (!workbook.SheetNames.length) {
        throw new Error("No worksheets found in XLSX file");
      }

      // Use first worksheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      this.logger.log(`Using worksheet: ${workbook.SheetNames[0]}`);

      // Convert to array of arrays (preserves original structure)
      const rawRows = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        raw: false,
        defval: ""
      }) as any[][];

      this.logger.log(`Extracted ${rawRows.length} raw rows from worksheet`);

      // Process raw rows into structured data
      return this.processRawRows(rawRows);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to parse XLSX file: ${errorMessage}`);
      throw new Error(`XLSX parsing failed: ${errorMessage}`);
    }
  }

  /**
   * Process raw XLSX rows into structured tariff data
   */
  private processRawRows(rawRows: any[][]): ProcessedTariffData[] {
    const processedData: ProcessedTariffData[] = [];

    // Skip header rows (first 3 rows based on sample CSV)
    const dataRows = rawRows.slice(3);

    for (const row of dataRows) {
      if (!row || row.length < 19) {
        this.logger.warn(`Skipping invalid row: ${JSON.stringify(row)}`);
        continue;
      }

      try {
        const processed = this.processRow(row);
        if (processed) {
          processedData.push(processed);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.warn(`Error processing row: ${errorMessage}`, { row });
      }
    }

    return processedData;
  }

  /**
   * Process a single row into structured tariff data
   */
  private processRow(row: any[]): ProcessedTariffData | null {
    const [
      tariffNumber,
      description,
      effectiveDate,
      expirationDate,
      censusQty1,
      censusQty2,
      censusQty3,
      adDuty,
      cvDuty,
      pga1,
      pga2,
      pga3,
      pga4,
      pga5,
      pga6,
      pga7,
      pga8,
      pga9,
      pga10
    ] = row;

    // Validate required fields
    if (!tariffNumber || !description) {
      return null;
    }

    // Clean and validate HTS code (must be exactly 10 digits)
    const hsCode = String(tariffNumber).replace(/\./g, "");
    if (!/^\d{10}$/.test(hsCode)) {
      this.logger.warn(`Invalid HTS code: ${tariffNumber} -> ${hsCode}`);
      return null;
    }

    // Parse dates
    const effectiveDateParsed = this.parseDate(effectiveDate);
    const expiryDateParsed = this.parseDate(expirationDate);

    if (!effectiveDateParsed || !expiryDateParsed) {
      this.logger.warn(`Invalid dates for HTS ${hsCode}: ${effectiveDate}, ${expirationDate}`);
      return null;
    }

    // Process census quantities (convert "NO" to null)
    const processCensusQty = (qty: any): string | null => {
      const str = String(qty || "").trim();
      return str === "NO" || str === "" ? null : str;
    };

    // Process duty flags (N -> false, Y -> true)
    const processDutyFlag = (flag: any): boolean | null => {
      const str = String(flag || "")
        .trim()
        .toUpperCase();
      if (str === "N") return false;
      if (str === "Y") return true;
      return null;
    };

    // Collect PGA codes (filter out empty values)
    const pgaCodes = [pga1, pga2, pga3, pga4, pga5, pga6, pga7, pga8, pga9, pga10]
      .map((code) => String(code || "").trim())
      .filter((code) => code && code !== "")
      .filter((code) => code.length > 0);

    return {
      hsCode,
      description: String(description).trim(),
      effectiveDate: effectiveDateParsed,
      expiryDate: expiryDateParsed,
      censusQty1: processCensusQty(censusQty1),
      censusQty2: processCensusQty(censusQty2),
      censusQty3: processCensusQty(censusQty3),
      adDuty: processDutyFlag(adDuty),
      cvDuty: processDutyFlag(cvDuty),
      pgaCodes: pgaCodes.length > 0 ? pgaCodes : null
    };
  }

  /**
   * Parse date from various formats
   */
  private parseDate(dateValue: any): Date | null {
    if (!dateValue) return null;

    if (dateValue instanceof Date) {
      return dateValue;
    }

    const parsed = new Date(dateValue);
    return isNaN(parsed.getTime()) ? null : parsed;
  }

  /**
   * Upsert processed tariff data to database with differential sync
   * This implements a complete daily replacement strategy:
   * 1. Delete tariffs not present in current file (expired/removed tariffs)
   * 2. Upsert tariffs from current file (new/updated tariffs)
   */
  private async upsertRows(processedData: ProcessedTariffData[]): Promise<TariffImportStats> {
    const stats: TariffImportStats = {
      inserted: 0,
      updated: 0,
      deleted: 0,
      processed: processedData.length,
      errors: 0
    };

    const BATCH_SIZE = 1000;
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      await queryRunner.startTransaction();

      // Step 1: Delete tariffs not present in current file
      // This ensures database only contains currently active tariffs from CBP
      const currentHsCodes = processedData.map((data) => data.hsCode);
      this.logger.log(`Current file contains ${currentHsCodes.length} tariffs`);

      if (currentHsCodes.length > 0) {
        // Delete tariffs (and their PGA relationships via cascade) not in current file
        const deleteResult = await queryRunner.manager
          .createQueryBuilder()
          .delete()
          .from(UsTariff)
          .where("hsCode NOT IN (:...codes)", { codes: currentHsCodes })
          .execute();

        stats.deleted = deleteResult.affected || 0;
        this.logger.log(`Deleted ${stats.deleted} tariffs no longer in source data`);
      }

      // Step 2: Upsert current tariffs in batches
      for (let i = 0; i < processedData.length; i += BATCH_SIZE) {
        const batch = processedData.slice(i, i + BATCH_SIZE);
        const batchStats = await this.upsertBatch(queryRunner, batch);

        stats.inserted += batchStats.inserted;
        stats.updated += batchStats.updated;
        stats.errors += batchStats.errors;

        this.logger.log(`Processed batch ${Math.floor(i / BATCH_SIZE) + 1}: ${JSON.stringify(batchStats)}`);
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Transaction committed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Transaction rolled back: ${errorMessage}`);
      throw error;
    } finally {
      await queryRunner.release();
    }

    return stats;
  }

  /**
   * Upsert a batch of tariff records
   */
  private async upsertBatch(
    queryRunner: any,
    batch: ProcessedTariffData[]
  ): Promise<Pick<TariffImportStats, "inserted" | "updated" | "errors">> {
    const stats = { inserted: 0, updated: 0, errors: 0 };

    for (const data of batch) {
      try {
        const existing = await queryRunner.manager.findOne(UsTariff, {
          where: { hsCode: data.hsCode }
        });

        if (existing) {
          // Update existing record
          await queryRunner.manager.update(
            UsTariff,
            { hsCode: data.hsCode },
            {
              description: data.description,
              effectiveDate: data.effectiveDate,
              expiryDate: data.expiryDate,
              censusQty1: data.censusQty1,
              censusQty2: data.censusQty2,
              censusQty3: data.censusQty3,
              adDuty: data.adDuty,
              cvDuty: data.cvDuty,
              pgaCodes: data.pgaCodes,
              lastEditDate: new Date()
            }
          );
          stats.updated++;
        } else {
          // Insert new record
          const newTariff = queryRunner.manager.create(UsTariff, {
            ...data,
            createDate: new Date(),
            lastEditDate: new Date()
          });
          await queryRunner.manager.save(newTariff);
          stats.inserted++;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.error(`Error upserting tariff ${data.hsCode}: ${errorMessage}`);
        stats.errors++;
      }
    }

    return stats;
  }

  /**
   * Step 3: Populate pivot table with tariff-PGA relationships
   */
  private async upsertPivotTable(processedData: ProcessedTariffData[]): Promise<{
    inserted: number;
    deleted: number;
    errors: number;
  }> {
    const stats = { inserted: 0, deleted: 0, errors: 0 };

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      await queryRunner.startTransaction();

      // Get all PGA requirements for lookup (now we just need to validate tariffFlagCodes exist)
      const pgaRequirements = await queryRunner.manager.find(UsPgaRequirement);
      const validPgaCodes = new Set(pgaRequirements.map((req) => req.tariffFlagCode));

      // Process each tariff's PGA relationships
      for (const data of processedData) {
        if (!data.pgaCodes || data.pgaCodes.length === 0) {
          continue;
        }

        try {
          // Delete existing relationships for this HS code
          const deleteResult = await queryRunner.manager.delete(UsTariffPgaRequirement, {
            hsCode: data.hsCode
          });
          stats.deleted += deleteResult.affected || 0;

          // Create new relationships
          for (const pgaCode of data.pgaCodes) {
            if (!validPgaCodes.has(pgaCode)) {
              this.logger.warn(`PGA requirement not found for code: ${pgaCode}`);
              continue;
            }

            const pivotRecord = queryRunner.manager.create(UsTariffPgaRequirement, {
              hsCode: data.hsCode,
              tariffFlagCode: pgaCode
            });

            await queryRunner.manager.save(pivotRecord);
            stats.inserted++;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          this.logger.error(`Error processing pivot relationships for ${data.hsCode}: ${errorMessage}`);
          stats.errors++;
        }
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Pivot table transaction committed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Pivot table transaction rolled back: ${errorMessage}`);
      throw error;
    } finally {
      await queryRunner.release();
    }

    return stats;
  }
}
