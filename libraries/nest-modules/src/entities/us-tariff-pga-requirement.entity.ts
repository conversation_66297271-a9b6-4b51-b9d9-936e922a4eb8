import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, Index, Check, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { UsPgaRequirement } from "./us-pga-requirement.entity";

@Entity("us_tariff_pga_requirement")
@Check("CHK_US_TARIFF_PGA_HS_CODE_LENGTH", 'char_length("hsCode") = 10')
@Index("IDX_US_TARIFF_PGA_HS_CODE", ["hsCode"])
@Index("IDX_US_TARIFF_PGA_TARIFF_FLAG_CODE", ["tariffFlagCode"])
@Unique("UQ_US_TARIFF_PGA_HS_CODE_TARIFF_FLAG_CODE", ["hsCode", "tariffFlagCode"])
export class UsTariffPgaRequirement extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", minLength: 10, maxLength: 10 })
  @Column({ length: 10 })
  hsCode: string;

  @ApiProperty({ type: "string", maxLength: 3 })
  @Column({ length: 3 })
  tariffFlagCode: string;

  @ApiPropertyOptional({
    type: "string",
    description: "Additional notes about this requirement for the specific tariff"
  })
  @Column({ type: "text", nullable: true })
  notes: string | null;

  // Relationships
  @ApiProperty({ type: () => UsPgaRequirement })
  @ManyToOne((type) => UsPgaRequirement, (requirement) => requirement.tariffRelations, {
    onDelete: "CASCADE"
  })
  usPgaRequirement: UsPgaRequirement;
}
