import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Check,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  Unique
} from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedUsTariff extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", minLength: 10, maxLength: 10 })
  @Column({ length: 10 })
  hsCode: string;

  @ApiProperty({ type: "string", format: "date" })
  @Column({ type: "date" })
  effectiveDate: Date;

  @ApiProperty({ type: "string", format: "date" })
  @Column({ type: "date" })
  expiryDate: Date;

  @ApiProperty()
  @Column({ type: "text" })
  description: string;

  @ApiPropertyOptional({
    description: "Census quantity 1 for statistical reporting"
  })
  @Column({ nullable: true })
  censusQty1: string | null;

  @ApiPropertyOptional({
    description: "Census quantity 2 for statistical reporting"
  })
  @Column({ nullable: true })
  censusQty2: string | null;

  @ApiPropertyOptional({
    description: "Census quantity 3 for statistical reporting"
  })
  @Column({ nullable: true })
  censusQty3: string | null;

  @ApiPropertyOptional({
    description: "Anti-dumping duty applicable"
  })
  @Column({ type: "boolean", nullable: true })
  adDuty: boolean | null;

  @ApiPropertyOptional({
    description: "Countervailing duty applicable"
  })
  @Column({ type: "boolean", nullable: true })
  cvDuty: boolean | null;

  @ApiPropertyOptional({
    type: [String],
    description: "Partner Government Agency codes"
  })
  @Column({ type: "text", array: true, nullable: true })
  pgaCodes: string[] | null;

  // Computed Fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @ApiPropertyOptional({
    description: "Unit of measure for commercial invoice aggregation"
  })
  uom: string | null;

  @AfterLoad()
  @AfterInsert()
  @AfterUpdate()
  compute() {
    this.displayName = `${this.hsCode} - ${this.description}`;

    // Find primary UOM from census quantities (first non-"NO" value)
    this.uom = [this.censusQty1, this.censusQty2, this.censusQty3].find((qty) => qty && qty !== "NO") || null;
  }

  /**
   * Get all census quantities as array (excluding "NO" and null values)
   */
  getCensusQuantities(): string[] {
    return [this.censusQty1, this.censusQty2, this.censusQty3].filter(
      (qty): qty is string => Boolean(qty) && qty !== "NO"
    );
  }

  /**
   * Check if this tariff requires census quantity reporting
   */
  requiresCensusReporting(): boolean {
    return this.getCensusQuantities().length > 0;
  }
}

@Entity("us_tariff")
@Check("VALID_HTS_CODE", 'char_length("hsCode") = 10')
@Unique("UNIQUE_US_HTS_CODE", ["hsCode"])
export class UsTariff extends SimplifiedUsTariff {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdUsTariffs, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedUsTariffs, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => CommercialInvoiceLine, (line) => line.usTariffCode)
  commercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;
}
