# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Claude Code Efficiency Guidelines

**Parallel Sub-Agent Usage:**
- Always use parallel sub-agents (Task tool) for independent information gathering tasks or anywhere you can break down the task into smaller independent tasks and/or parallelize the work
- Consolidate and verify results from multiple sub-agents before proceeding
- Ideal for: codebase exploration, pattern analysis, multi-file searches, parallel testing
- Always check sub-agent outputs for consistency and completeness
- Use single consolidated response after parallel work is complete

## Project Overview

This is Claro, a customs automation platform built as a Rush monorepo. The system processes trade documents, manages customs filings, and provides automated compliance checking for import/export operations.

**Main Applications:**
- `portal` - Customer-facing React frontend
- `portal-api` - Main NestJS backend API with LLM integration
- `backoffice` - Admin React frontend  
- `backoffice-api` - Admin NestJS backend
- `bullmq-board` - Queue monitoring dashboard
- `cloud-functions` - Firebase cloud functions

**Shared Libraries:**
- `libraries/nest-modules` - Shared NestJS modules, entities, DTOs, and services
- `libraries/ui` - Shared React UI components
- `tools/utils` - Shared utilities

## Common Commands

**Development Setup:**
```bash
# Install packages (use Rush, never npm install directly)
rush update

# Build all projects
rush build

# Start development servers
rush dev                    # All services in parallel
rush fe                     # Frontend portal only  
rush start -t portal        # Specific project
rush start -t portal-api    # API server with watch mode

# Start supporting services (Redis, Postgres, etc.)
rush services
```

**Testing:**
```bash
# NEVER run Jest tests - use core-agent testing scripts instead
# cd apps/portal-api && rushx jest  # ❌ DON'T USE

# Use core-agent testing scripts (preferred)
cd apps/portal-api && ./src/core-agent/testing/run-e2e-with-logs.sh

# Check TypeScript in frontend apps
cd apps/portal && rushx lint

# Use parallel sub-agents for testing multiple components or scenarios
# Consolidate test results before reporting status
```

**Database Operations:**
```bash
# Generate migration (from portal-api directory)
cd apps/portal-api && npx typeorm migration:generate src/migrations/MigrationName -d src/data-source.ts

# Run migrations
cd apps/portal-api && npx typeorm migration:run -d src/data-source.ts

# Database queries (NEVER use direct SQL/psql)
cd apps/portal-api && node src/core-agent/testing/db-query.js orgs
cd apps/portal-api && node src/core-agent/testing/db-query.js sql "SELECT * FROM shipment LIMIT 5"
```

## Migration Details

**Migration Locations:**
- Migrations for Claro are in '/home/<USER>/dev/Claro/libraries/nest-modules/src/migrations'

## Architecture Overview

[... rest of the original file content remains unchanged ...]