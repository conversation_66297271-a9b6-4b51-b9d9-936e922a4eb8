import { createKeyv } from "@keyv/redis";
import { BullModule } from "@nestjs/bullmq";
import { CacheModule } from "@nestjs/cache-manager";
import { Logger, MiddlewareConsumer, Module, NestModule, RequestMethod } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { JwtModule } from "@nestjs/jwt";
import { ScheduleModule } from "@nestjs/schedule";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  ApiTemplateModule,
  AuthModule,
  BaseDocusignModule,
  BaseImporterModule,
  CanadaAntiDumpingModule,
  CanadaExciseTaxCodeModule,
  CanadaGstExemptCodeModule,
  CanadaOgdModule,
  CanadaSimaCodeModule,
  CanadaSubLocationModule,
  CanadaTariffModule,
  CanadaTreatmentCodeModule,
  CanadaVfdCodeModule,
  CandataAccountAuthType,
  CandataModule,
  CarmModule,
  CountryModule,
  DatabaseValidatorModule,
  DocumentTypeModule,
  ENTITY_LIST,
  FirebaseModule,
  GlobalSearchModule,
  LoginMethod,
  MatchingRuleModule,
  NodeEnv,
  OrganizationCustomsBroker,
  RequestLoggerMiddleware,
  TemplateManagerModule,
  TrackingHistoryModule,
  TransactionalEventEmitterModule,
  UserModule
} from "nest-modules";

import { ClsModule } from "nestjs-cls";

import { AggregationModule } from "./aggregation/aggregation.module";
import { AppController } from "./app.controller";
import { CertificateModule } from "./certificate/certificate.module";
import { CommercialInvoiceModule } from "./commercial-invoice/commercial-invoice.module";
import TypeOrmNestJsLogger from "./common/typeorm-nestjs.logger";
import { CoreAgentModule } from "./core-agent/core-agent.module";
import { CronModule } from "./cron/cron.module";
import { DebugModule } from "./debug/debug.module";
import { DocumentModule } from "./document/document.module";
import { EmailModule } from "./email/email.module";
import { ImporterModule } from "./importer/importer.module";
import { LlmModule } from "./llm";
import { LocationModule } from "./location/location.module";
import { OgdFilingModule } from "./ogd-filing/ogd-filing.module";
import { OnboardingModule } from "./onboarding/onboarding.module";
import { ProductModule } from "./product/product.module";
import { ShipmentTrackingModule } from "./shipment-tracking/shipment-tracking.module";
import { ShipmentModule } from "./shipment/shipment.module";
import { SimaFilingModule } from "./sima-filing/sima-filing.module";
import { StorageModule } from "./storage/storage.module";
import { TradePartnerModule } from "./trade-partner/trade-partner.module";
import { UnitConversionModule } from "./unit-conversion/unit-conversion.module";
import { UsTariffModule } from "./us-tariff/us-tariff.module";
import { WorkerModule } from "./worker/worker.module";

// disable BullModule if REDIS_HOST and REDIS_PORT are not set
const bullModuleImports = () => {
  if (process.env.REDIS_HOST && process.env.REDIS_PORT) {
    return [
      BullModule.forRoot({
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT)
        }
      })
    ];
  }

  Logger.warn("Redis connection not configured, skipping BullModule", "AppModule");
  return [];
};

const cacheModuleImports = () => {
  if (process.env.REDIS_HOST && process.env.REDIS_PORT) {
    return [
      CacheModule.registerAsync({
        isGlobal: true,
        useFactory: async () => {
          return {
            stores: [
              createKeyv({
                url: `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`
              })
            ]
          };
        }
      })
    ];
  }

  Logger.warn("Redis connection not configured, skipping CacheModule", "AppModule");
  return [];
};

export const NODE_ENV = process.env.NODE_ENV || NodeEnv.LOCAL;

@Module({
  imports: [
    ScheduleModule.forRoot(),
    JwtModule.register({ global: true }),
    ClsModule.forRoot({
      global: true,
      interceptor: {
        mount: true,
        setup: (cls, context) => {
          const request = context.switchToHttp().getRequest();
          if ("user" in request && request.user?.organization) {
            cls.set("USER_PERMISSION", request.user.permission);
            cls.set("ORGANIZATION_ID", request.user.organization.id);
          }
        }
      }
    }),
    ConfigModule.forRoot({
      envFilePath: `.env.${NODE_ENV}`,
      isGlobal: true
    }),
    DebugModule.forRoot(),
    ...bullModuleImports(),
    ...cacheModuleImports(),
    TypeOrmModule.forRoot({
      type: "postgres",
      applicationName: "portal-api",
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      // If feature flag is set, that means we are launching a worker, so we will disable the sync
      synchronize: process.env.FEATURE ? false : true,
      // enable verbose loggin when VERBOSE is set, otherwise log only error and warn
      logger: new TypeOrmNestJsLogger(process.env.VERBOSE !== undefined ? "all" : ["error", "warn"]),
      // track and log queries that is taking more than 1 second
      // @see: https://orkhan.gitbook.io/typeorm/docs/logging#log-long-running-queries
      maxQueryExecutionTime: 1000,
      ssl: process.env.DB_SSL_ENABLED === "true",
      extra: {
        // connectionTimeoutMillis will be set by TypeORM from connectTimeoutMS,
        idleTimeoutMillis: 30000,
        // TODO: use rds ca certificate when deployed to aws
        // ssl: { rejectUnauthorized: false }
        ...(process.env.DB_SSL_ENABLED === "true" ? { ssl: { rejectUnauthorized: false } } : {})
      },
      cache: {
        type: "ioredis",
        alwaysEnabled: false,
        options: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT)
        }
      },
      entities: ENTITY_LIST,
      connectTimeoutMS: 5000,
      poolSize: 20
    }),
    EventEmitterModule.forRoot({
      global: true,
      ignoreErrors: true
    }),
    ScheduleModule.forRoot(),
    AuthModule.register({
      accessTokenSecret: process.env.ACCESS_TOKEN_SECRET,
      refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET,
      accessTokenExpiresInSec: parseInt(process.env.ACCESS_TOKEN_EXPIRES_IN_SEC),
      refreshTokenExpiresInSec: parseInt(process.env.REFRESH_TOKEN_EXPIRES_IN_SEC),
      refreshTokenGracePeriodSec: parseInt(process.env.REFRESH_TOKEN_GRACE_PERIOD_SEC),
      googleOAuthClientId: process.env.GOOGLE_OAUTH_CLIENT_ID,
      allowedLoginMethods: [LoginMethod.EMAIL]
    }),
    TemplateManagerModule.forRoot(),
    LlmModule.register({
      openaiApiKey: process.env.OPENAI_API_KEY,
      deepseekApiKey: process.env.DEEPSEEK_API_KEY
    }),
    UserModule.register({
      user: {
        readMany: true,
        readOne: true,
        create: true,
        update: true,
        delete: true
      },
      organization: {
        readMany: true,
        readOne: true,
        create: false,
        update: false,
        delete: false
      }
    }),
    FirebaseModule.register({
      projectId: process.env.FIREBASE_PROJECT_ID,
      privateKey: process.env.FIREBASE_PRIVATE_KEY,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL
    }),
    CarmModule.register({
      appId: process.env.CARM_APP_ID
    }),
    CanadaTariffModule.register({
      readMany: true,
      readOne: true,
      sync: false
    }),
    CanadaOgdModule.register({
      readMany: true,
      readOne: true,
      create: false,
      update: false,
      delete: false
    }),
    CanadaTreatmentCodeModule.register({
      readMany: true,
      readOne: true,
      create: false,
      update: false,
      delete: false
    }),
    CanadaGstExemptCodeModule.register({
      readMany: true,
      readOne: true,
      create: false,
      update: false,
      delete: false
    }),
    CanadaSimaCodeModule.register({
      readMany: true,
      readOne: true,
      create: false,
      update: false,
      delete: false
    }),
    CanadaVfdCodeModule.register({
      readMany: true,
      readOne: true,
      create: false,
      update: false,
      delete: false
    }),
    CanadaAntiDumpingModule.register({
      readMany: true,
      readOne: true,
      create: false,
      update: false,
      delete: false
    }),
    CanadaExciseTaxCodeModule.register({
      readMany: true,
      readOne: true,
      create: false,
      update: false,
      delete: false
    }),
    DocumentTypeModule.register({
      readMany: true,
      readOne: true,
      create: false,
      update: false,
      delete: false
    }),
    MatchingRuleModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true,
      query: true
    }),
    CountryModule,
    LocationModule,
    TradePartnerModule,
    BaseImporterModule,
    ImporterModule,
    BaseDocusignModule.register({
      integrationKey: process.env.DOCUSIGN_INTEGRATION_KEY,
      secretKey: process.env.DOCUSIGN_SECRET_KEY,
      poaTemplateId: process.env.DOCUSIGN_POA_TEMPLATE_ID,
      baseUrl: process.env.DOCUSIGN_BASE_URL
    }),
    ShipmentTrackingModule.forRoot(),
    ShipmentModule.forRoot(),
    ProductModule,
    CommercialInvoiceModule,
    CanadaSubLocationModule,
    StorageModule.forRoot({
      awsRegion: process.env.AWS_REGION,
      awsS3Bucket: process.env.AWS_S3_BUCKET
    }),
    DocumentModule.forRoot({
      tikaEndpointUrl: process.env.TIKA_ENDPOINT_URL,
      llamaCloudApiKey: process.env.LLAMA_CLOUD_API_KEY,
      awsRegion: process.env.AWS_REGION,
      awsAccessKey: process.env.AWS_ACCESS_KEY,
      awsSecretKey: process.env.AWS_SECRET_KEY
    }),
    OgdFilingModule.forRoot(),
    SimaFilingModule.forRoot(),
    TrackingHistoryModule.register(),
    CandataModule.register({
      accounts: [
        {
          account: OrganizationCustomsBroker.USAV,
          authType: CandataAccountAuthType.API_KEY,
          baseUrl: process.env.USAV_CANDATA_BASE_URL,
          apiKey: process.env.USAV_CANDATA_TOKEN
        },
        {
          account: OrganizationCustomsBroker.CLARO,
          authType: CandataAccountAuthType.CLIENT_ID_AND_SECRET,
          baseUrl: process.env.CLARO_CANDATA_BASE_URL,
          authUrl: process.env.CLARO_CANDATA_AUTH_URL,
          clientId: process.env.CLARO_CANDATA_CLIENT_ID,
          clientSecret: process.env.CLARO_CANDATA_CLIENT_SECRET
        }
      ],
      appId: process.env.CANDATA_REMOTE_SERVER_APP_ID
    }),
    DatabaseValidatorModule,
    CronModule.register(),
    AggregationModule.forRoot(),
    EmailModule.forRoot(),
    ApiTemplateModule.register({
      apiKey: process.env.API_TEMPLATE_API_KEY
    }),
    TransactionalEventEmitterModule,
    CoreAgentModule,
    UnitConversionModule,
    OnboardingModule,
    GlobalSearchModule,
    UsTariffModule.forRoot(),
    WorkerModule,
    CertificateModule
  ],
  controllers: [AppController],
  providers: []
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes({ path: "*", method: RequestMethod.ALL });
  }
}
