#!/usr/bin/env node

const axios = require('axios');

/**
 * Direct test of Apify Key-Value Store contents
 * Check what files are actually available in the store
 */

async function testKVStoreDirect() {
  console.log("🗄️  Testing Apify Key-Value Store Direct Access");
  console.log("===============================================");

  try {
    // Get environment variables
    const apifyToken = process.env.APIFY_TOKEN;
    if (!apifyToken) {
      throw new Error("APIFY_TOKEN environment variable not set");
    }

    const baseUrl = "https://api.apify.com/v2";
    const storeName = "antek-netchb-us-tariff-downloader";
    
    const headers = {
      "Authorization": `Bearer ${apifyToken}`,
      "Accept": "application/json"
    };

    console.log("🔑 Using Apify token:", apifyToken.substring(0, 20) + "...");
    console.log("🗄️  Store name:", storeName);

    // Step 1: List all keys in the store
    console.log("\n📋 Step 1: Listing all keys in the store...");
    
    try {
      const keysResponse = await axios.get(
        `${baseUrl}/key-value-stores/${storeName}/keys`,
        { headers }
      );

      console.log("✅ Store accessible");
      console.log("📊 Total keys found:", keysResponse.data.count);
      
      if (keysResponse.data.items && keysResponse.data.items.length > 0) {
        console.log("\n📂 Available keys:");
        keysResponse.data.items.forEach((item, index) => {
          console.log(`  ${index + 1}. ${item.key} (${item.size} bytes, modified: ${item.modifiedAt})`);
        });

        // Step 2: Try to find US_TARIFF_* keys
        console.log("\n🔍 Step 2: Looking for US_TARIFF_* files...");
        
        const tariffKeys = keysResponse.data.items.filter(item => 
          item.key.startsWith('US_TARIFF_') && !item.key.includes('_metadata')
        );

        if (tariffKeys.length > 0) {
          console.log(`📁 Found ${tariffKeys.length} US Tariff file(s):`);
          
          for (const tariffKey of tariffKeys) {
            console.log(`\n📄 Key: ${tariffKey.key}`);
            console.log(`📊 Size: ${(tariffKey.size / 1024 / 1024).toFixed(2)} MB`);
            console.log(`🕒 Modified: ${tariffKey.modifiedAt}`);
            
            // Try to download a small portion to verify it's an XLSX file
            try {
              const fileResponse = await axios.get(
                `${baseUrl}/key-value-stores/${storeName}/records/${tariffKey.key}`,
                { 
                  headers,
                  responseType: 'arraybuffer',
                  maxContentLength: 1000 // Just get first 1KB to check file signature
                }
              );
              
              const buffer = Buffer.from(fileResponse.data);
              const isXLSX = buffer.length >= 4 && 
                           buffer[0] === 0x50 && buffer[1] === 0x4B; // PK signature for ZIP/XLSX
              
              console.log(`📋 File type: ${isXLSX ? 'Valid XLSX/ZIP file' : 'Not an XLSX file'}`);
              
              if (isXLSX) {
                console.log("✅ This file can be downloaded!");
                
                // Test downloading the full file
                if (process.argv.includes('--download-test')) {
                  console.log("📥 Testing full file download...");
                  const fullResponse = await axios.get(
                    `${baseUrl}/key-value-stores/${storeName}/records/${tariffKey.key}`,
                    { 
                      headers,
                      responseType: 'arraybuffer'
                    }
                  );
                  
                  console.log(`✅ Successfully downloaded ${(fullResponse.data.byteLength / 1024 / 1024).toFixed(2)} MB`);
                  
                  // Save to test directory
                  const fs = require('fs');
                  const path = require('path');
                  const testDir = path.join(__dirname, "../../../.ai-reference/us-tariff-test");
                  if (!fs.existsSync(testDir)) {
                    fs.mkdirSync(testDir, { recursive: true });
                  }
                  
                  const fileName = `downloaded-${tariffKey.key}.xlsx`;
                  const filePath = path.join(testDir, fileName);
                  fs.writeFileSync(filePath, Buffer.from(fullResponse.data));
                  console.log(`💾 File saved to: ${filePath}`);
                }
              }
              
            } catch (downloadError) {
              console.log(`❌ Failed to access file: ${downloadError.response?.status || downloadError.message}`);
            }
          }
        } else {
          console.log("📭 No US_TARIFF_* files found in store");
        }

        // Step 3: Check for metadata files
        console.log("\n🔍 Step 3: Looking for metadata files...");
        const metadataKeys = keysResponse.data.items.filter(item => 
          item.key.includes('_metadata')
        );

        if (metadataKeys.length > 0) {
          console.log(`📁 Found ${metadataKeys.length} metadata file(s):`);
          for (const metaKey of metadataKeys) {
            console.log(`  - ${metaKey.key} (${metaKey.size} bytes)`);
            
            // Try to read metadata
            try {
              const metaResponse = await axios.get(
                `${baseUrl}/key-value-stores/${storeName}/records/${metaKey.key}`,
                { headers }
              );
              console.log(`    📋 Content:`, JSON.stringify(metaResponse.data, null, 4));
            } catch (metaError) {
              console.log(`    ❌ Failed to read: ${metaError.response?.status || metaError.message}`);
            }
          }
        } else {
          console.log("📭 No metadata files found");
        }

      } else {
        console.log("📭 Store is empty");
      }

    } catch (storeError) {
      console.error("❌ Failed to access store:", storeError.response?.data || storeError.message);
      
      if (storeError.response?.status === 404) {
        console.error("🔍 Store might not exist or token doesn't have access");
      }
    }

    console.log("\n✅ Key-Value Store investigation completed!");

  } catch (error) {
    console.error("💥 Investigation failed:", error.message);
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const helpRequested = args.includes('--help') || args.includes('-h');

if (helpRequested) {
  console.log(`
Apify Key-Value Store Direct Investigation

Usage: node test-kv-store-direct.js [options]

Options:
  --download-test     Actually download found XLSX files for testing
  --help, -h         Show this help message

Environment:
  APIFY_TOKEN        Required - Apify API token

Examples:
  # List what's in the store
  node test-kv-store-direct.js
  
  # List and download files for testing
  node test-kv-store-direct.js --download-test
`);
  process.exit(0);
}

// Main execution
if (require.main === module) {
  testKVStoreDirect();
}