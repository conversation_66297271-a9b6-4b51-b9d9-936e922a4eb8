#!/usr/bin/env node

const fs = require("fs");
const path = require("path");

/**
 * Direct test of Apify US Tariff Integration
 * Tests authentication, actor connection, and XLSX download functionality
 * Following core-agent testing patterns
 */

async function testApifyIntegration() {
  console.log("🧪 Testing Apify US Tariff Integration");
  console.log("=====================================");

  try {
    // Import required modules
    console.log("\n🔧 Setting up NestJS application context...");

    const { NestFactory, ContextIdFactory } = require("@nestjs/core");
    const { AppModule } = require("../../../dist/app.module");
    const { UsTariffApifyService, ApifyService } = require("nest-modules");

    console.log("🚀 Creating NestJS application context...");
    const contextId = ContextIdFactory.create();
    const app = await NestFactory.createApplicationContext(AppModule);

    // Inject request context (demo organization)
    app.registerRequestByContextId(
      {
        user: {
          permission: "backoffice-admin",
          organization: {
            id: 3 // Demo organization
          }
        }
      },
      contextId
    );

    try {
      // Get services with request context
      const apifyService = await app.resolve(ApifyService, contextId);
      const usTariffApifyService = await app.resolve(UsTariffApifyService, contextId);
      console.log("✅ Got Apify services");

      // Step 1: Test basic Apify authentication
      console.log("\n🔐 Step 1: Testing Apify Authentication...");
      await testApifyAuthentication(apifyService);

      // Step 2: Test US Tariff actor configuration
      console.log("\n🎭 Step 2: Testing US Tariff Actor Configuration...");
      await testActorConfiguration(usTariffApifyService);

      // Step 3: Test file existence check
      console.log("\n📋 Step 3: Testing File Existence Check...");
      await testFileExistenceCheck(usTariffApifyService);

      // Step 4: Test file download
      console.log("\n📥 Step 4: Testing File Download...");
      await testFileDownload(usTariffApifyService);

      // Step 5: Test actor run (optional - only if force download requested)
      console.log("\n🚀 Step 5: Testing Actor Run (if needed)...");
      await testActorRun(usTariffApifyService);

      console.log("\n✅ All Apify integration tests completed successfully!");
    } finally {
      await app.close();
    }
  } catch (error) {
    console.error("💥 Apify integration test failed:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

/**
 * Test basic Apify authentication
 */
async function testApifyAuthentication(apifyService) {
  try {
    console.log("🔑 Testing Apify API authentication...");
    
    // Test authentication by making a simple API call
    // We'll try to get a non-existent actor run to test auth without side effects
    try {
      await apifyService.getRun("test-non-existent-run-id");
    } catch (error) {
      // We expect this to fail with 404, but if it's 401/403, that's an auth issue
      if (error.response?.status === 404) {
        console.log("✅ Authentication successful (404 expected for non-existent run)");
      } else if (error.response?.status === 401 || error.response?.status === 403) {
        throw new Error(`Authentication failed: ${error.response.status} - ${error.response.statusText}`);
      } else {
        console.log(`✅ Authentication working (unexpected error type: ${error.message})`);
      }
    }
  } catch (error) {
    console.error("❌ Authentication test failed:", error.message);
    throw error;
  }
}

/**
 * Test US Tariff actor configuration
 */
async function testActorConfiguration(usTariffApifyService) {
  try {
    console.log("🎭 Testing US Tariff actor configuration...");
    
    // Test with today's date
    const today = new Date().toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit", 
      year: "2-digit"
    }).replace(/\//g, "");
    
    console.log(`📅 Using date: ${today}`);
    console.log("ℹ️  Testing metadata retrieval (this tests actor and store configuration)...");
    
    const metadata = await usTariffApifyService.getTariffMetadata(today);
    console.log("✅ Actor configuration valid");
    console.log(`📊 Metadata result: ${metadata ? 'Found' : 'Not found'}`);
    
    if (metadata) {
      console.log("📋 Metadata details:", JSON.stringify(metadata, null, 2));
    }
  } catch (error) {
    console.error("❌ Actor configuration test failed:", error.message);
    throw error;
  }
}

/**
 * Test file existence check
 */
async function testFileExistenceCheck(usTariffApifyService) {
  try {
    console.log("📋 Testing file existence check...");
    
    // Test with today's date
    const today = new Date().toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "2-digit"
    }).replace(/\//g, "");
    
    console.log(`📅 Checking if file exists for date: ${today}`);
    const fileExists = await usTariffApifyService.tariffFileExists(today);
    console.log(`📊 File exists: ${fileExists}`);
    
    if (fileExists) {
      console.log("✅ File existence check successful - file found");
    } else {
      console.log("ℹ️  File existence check successful - no file found for today (this is normal)");
    }
  } catch (error) {
    console.error("❌ File existence check failed:", error.message);
    throw error;
  }
}

/**
 * Test file download
 */
async function testFileDownload(usTariffApifyService) {
  try {
    console.log("📥 Testing file download...");
    
    // First check if today's file exists
    const today = new Date().toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "2-digit"
    }).replace(/\//g, "");
    
    const fileExists = await usTariffApifyService.tariffFileExists(today);
    
    if (!fileExists) {
      console.log("ℹ️  No file exists for today, testing download will fail as expected");
      try {
        await usTariffApifyService.downloadTariffFile(today);
        console.log("❌ Unexpected: Download succeeded when file should not exist");
      } catch (error) {
        console.log("✅ Download correctly failed for non-existent file:", error.message);
      }
    } else {
      console.log("📥 File exists, attempting download...");
      const startTime = Date.now();
      
      const fileBuffer = await usTariffApifyService.downloadTariffFile(today);
      const downloadTime = Date.now() - startTime;
      
      console.log(`✅ Download successful!`);
      console.log(`📊 File size: ${(fileBuffer.length / 1024 / 1024).toFixed(2)} MB`);
      console.log(`⏱️  Download time: ${downloadTime}ms`);
      
      // Verify it's an XLSX file by checking magic bytes
      if (fileBuffer.length > 4) {
        const magicBytes = fileBuffer.subarray(0, 4);
        const isZip = magicBytes[0] === 0x50 && magicBytes[1] === 0x4B; // PK signature
        const isOldExcel = magicBytes[0] === 0xD0 && magicBytes[1] === 0xCF; // OLE2 signature for older Excel formats
        console.log(`📋 File format check: ${isZip ? 'Valid ZIP/XLSX format' : isOldExcel ? 'Valid Excel format' : 'Unknown file format'}`);
      }
      
      // Optionally save file for inspection
      const testDir = path.join(__dirname, "../../../.ai-reference/us-tariff-test");
      if (!fs.existsSync(testDir)) {
        fs.mkdirSync(testDir, { recursive: true });
      }
      
      const testFilePath = path.join(testDir, `test-download-${today}.xlsx`);
      fs.writeFileSync(testFilePath, fileBuffer);
      console.log(`💾 Test file saved to: ${testFilePath}`);
    }
  } catch (error) {
    console.error("❌ File download test failed:", error.message);
    throw error;
  }
}

/**
 * Test actor run (only if explicitly requested)
 */
async function testActorRun(usTariffApifyService) {
  try {
    console.log("🚀 Testing actor run capability...");
    
    // Only run actor if --force-download is specified
    const forceDownload = process.argv.includes('--force-download');
    
    if (!forceDownload) {
      console.log("ℹ️  Skipping actor run test (use --force-download to test actor execution)");
      console.log("ℹ️  This prevents unnecessary actor runs during routine testing");
      return;
    }
    
    console.log("⚠️  Running actor with force download - this will consume Apify credits!");
    
    const today = new Date().toLocaleDateString("en-US", {
      month: "2-digit",
      day: "2-digit",
      year: "2-digit"
    }).replace(/\//g, "");
    
    console.log(`🎯 Starting actor run for date: ${today}`);
    const startTime = Date.now();
    
    const actorRun = await usTariffApifyService.runTariffDownload(today, true);
    console.log(`✅ Actor run started successfully`);
    console.log(`🔄 Run ID: ${actorRun.data?.id}`);
    console.log(`📊 Status: ${actorRun.data?.status}`);
    
    const initTime = Date.now() - startTime;
    console.log(`⏱️  Actor start time: ${initTime}ms`);
    
    // Note: We don't wait for completion here to avoid long test times
    console.log("ℹ️  Actor is running in background. Use the main test script to monitor completion.");
  } catch (error) {
    console.error("❌ Actor run test failed:", error.message);
    throw error;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const helpRequested = args.includes('--help') || args.includes('-h');

if (helpRequested) {
  console.log(`
Apify US Tariff Integration Test

Usage: node test-apify-integration.js [options]

Options:
  --force-download    Run the Apify actor (consumes credits)
  --help, -h         Show this help message

Examples:
  # Basic integration test (recommended)
  node test-apify-integration.js
  
  # Test with actor run (use sparingly)
  node test-apify-integration.js --force-download
`);
  process.exit(0);
}

// Main execution
if (require.main === module) {
  testApifyIntegration();
}