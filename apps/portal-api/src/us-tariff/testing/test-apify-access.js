#!/usr/bin/env node

const axios = require('axios');

/**
 * Comprehensive test of Apify access and actor configuration
 * Find out what we actually have access to
 */

async function testApifyAccess() {
  console.log("🔍 Comprehensive Apify Access Test");
  console.log("==================================");

  try {
    // Get environment variables
    const apifyToken = process.env.APIFY_TOKEN;
    if (!apifyToken) {
      throw new Error("APIFY_TOKEN environment variable not set");
    }

    const baseUrl = "https://api.apify.com/v2";
    
    const headers = {
      "Authorization": `Bearer ${apifyToken}`,
      "Accept": "application/json"
    };

    console.log("🔑 Using Apify token:", apifyToken.substring(0, 20) + "...");

    // Step 1: List all actors we have access to
    console.log("\n🎭 Step 1: Listing available actors...");
    
    try {
      const actorsResponse = await axios.get(
        `${baseUrl}/acts`,
        { headers }
      );

      console.log("✅ Actor API accessible");
      console.log("📊 Total actors found:", actorsResponse.data.count);
      
      if (actorsResponse.data.items && actorsResponse.data.items.length > 0) {
        console.log("\n📂 Available actors:");
        actorsResponse.data.items.forEach((actor, index) => {
          console.log(`  ${index + 1}. ${actor.name} (${actor.id})`);
          if (actor.name.includes('tariff') || actor.name.includes('netchb')) {
            console.log(`      ⭐ US Tariff related actor found!`);
          }
        });

        // Check for the specific actor
        const targetActor = actorsResponse.data.items.find(actor => 
          actor.name === 'antek~netchb-us-tariff-downloader'
        );

        if (targetActor) {
          console.log(`\n✅ Found target actor: ${targetActor.name}`);
          console.log(`📋 Actor ID: ${targetActor.id}`);
          console.log(`📝 Description: ${targetActor.description || 'No description'}`);
          
          // Try to get actor details
          try {
            const actorDetailResponse = await axios.get(
              `${baseUrl}/acts/${targetActor.id}`,
              { headers }
            );
            
            console.log("📊 Actor details accessible");
            console.log("🔧 Default run configuration:", JSON.stringify(actorDetailResponse.data.defaultRunOptions, null, 2));
            
          } catch (detailError) {
            console.log("❌ Failed to get actor details:", detailError.response?.data || detailError.message);
          }
          
        } else {
          console.log(`\n❌ Target actor 'antek~netchb-us-tariff-downloader' not found`);
          console.log("🔍 Check if actor name is correct or if we have access");
        }

      } else {
        console.log("📭 No actors found");
      }

    } catch (actorError) {
      console.error("❌ Failed to list actors:", actorError.response?.data || actorError.message);
    }

    // Step 2: List all key-value stores
    console.log("\n🗄️  Step 2: Listing available key-value stores...");
    
    try {
      const storesResponse = await axios.get(
        `${baseUrl}/key-value-stores`,
        { headers }
      );

      console.log("✅ Key-value store API accessible");
      console.log("📊 Total stores found:", storesResponse.data.count);
      
      if (storesResponse.data.items && storesResponse.data.items.length > 0) {
        console.log("\n📂 Available stores:");
        storesResponse.data.items.forEach((store, index) => {
          console.log(`  ${index + 1}. ${store.name || store.id} (${store.id})`);
          if (store.name && (store.name.includes('tariff') || store.name.includes('netchb'))) {
            console.log(`      ⭐ US Tariff related store found!`);
          }
        });

        // Check for the specific store
        const targetStore = storesResponse.data.items.find(store => 
          store.name === 'antek-netchb-us-tariff-downloader' || store.id === 'antek-netchb-us-tariff-downloader'
        );

        if (targetStore) {
          console.log(`\n✅ Found target store: ${targetStore.name || targetStore.id}`);
          console.log(`📋 Store ID: ${targetStore.id}`);
          
          // Try to access this store
          try {
            const storeKeysResponse = await axios.get(
              `${baseUrl}/key-value-stores/${targetStore.id}/keys`,
              { headers }
            );
            
            console.log("📊 Store accessible");
            console.log("📂 Keys in store:", storeKeysResponse.data.count);
            
            if (storeKeysResponse.data.items && storeKeysResponse.data.items.length > 0) {
              console.log("\n📄 Files in store:");
              storeKeysResponse.data.items.forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.key} (${(item.size / 1024).toFixed(1)} KB, ${item.modifiedAt})`);
              });
            }
            
          } catch (storeAccessError) {
            console.log("❌ Failed to access target store:", storeAccessError.response?.data || storeAccessError.message);
          }
          
        } else {
          console.log(`\n❌ Target store 'antek-netchb-us-tariff-downloader' not found`);
          console.log("🔍 Check if store name is correct or if it was created");
        }

      } else {
        console.log("📭 No key-value stores found");
      }

    } catch (storeError) {
      console.error("❌ Failed to list stores:", storeError.response?.data || storeError.message);
    }

    // Step 3: Test user info/account access
    console.log("\n👤 Step 3: Testing user account access...");
    
    try {
      const userResponse = await axios.get(
        `${baseUrl}/users/me`,
        { headers }
      );

      console.log("✅ User API accessible");
      console.log("👤 User:", userResponse.data.username);
      console.log("🏢 Plan:", userResponse.data.plan);
      
    } catch (userError) {
      console.log("❌ Failed to get user info:", userError.response?.data || userError.message);
    }

    console.log("\n🔍 Comprehensive access test completed!");
    console.log("\n💡 Summary:");
    console.log("   - Check if the actor name/ID is correct");
    console.log("   - Check if the key-value store name/ID is correct");
    console.log("   - Verify the token has access to the required resources");

  } catch (error) {
    console.error("💥 Access test failed:", error.message);
    process.exit(1);
  }
}

// Main execution
if (require.main === module) {
  testApifyAccess();
}