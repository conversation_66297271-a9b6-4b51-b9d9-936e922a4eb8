#!/usr/bin/env node

const axios = require('axios');

/**
 * Test the actual HTTP endpoints for US Tariff Apify integration
 * Tests authentication and sync endpoint as specified by user
 */

async function testHttpEndpoints() {
  console.log("🌐 Testing US Tariff HTTP Endpoints");
  console.log("=====================================");

  const baseUrl = 'http://localhost:5001';
  
  try {
    // Step 1: Get authentication token
    console.log("\n🔐 Step 1: Getting authentication token...");
    
    const authResponse = await axios.post(`${baseUrl}/auth/login`, {
      loginMethod: "email",
      email: "<EMAIL>", 
      password: "password"
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const token = authResponse.data.access_token;
    if (!token) {
      throw new Error("No access token received from login");
    }
    
    console.log("✅ Authentication successful");
    console.log(`📋 Token received: ${token.substring(0, 20)}...`);

    // Step 2: Test Apify sync endpoint  
    console.log("\n📥 Step 2: Testing Apify sync endpoint...");
    
    try {
      const syncResponse = await axios.post(`${baseUrl}/admin/us-tariffs/sync-from-apify`, {
        forceDownload: false  // Start with false to avoid unnecessary actor runs
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000  // 30 second timeout
      });

      console.log("✅ Sync endpoint accessible");
      console.log("📊 Response status:", syncResponse.status);
      console.log("📋 Response data:", JSON.stringify(syncResponse.data, null, 2));
      
    } catch (syncError) {
      if (syncError.response) {
        console.log(`⚠️  Sync endpoint returned: ${syncError.response.status} - ${syncError.response.statusText}`);
        console.log("📋 Error response:", JSON.stringify(syncError.response.data, null, 2));
        
        // Check if it's a expected "no file available" type error
        if (syncError.response.status === 404 || 
            (syncError.response.data && syncError.response.data.message && 
             syncError.response.data.message.includes('No file found'))) {
          console.log("ℹ️  This is expected when no file exists for today");
        }
      } else {
        throw syncError;
      }
    }

    // Step 3: Test with force download (only if explicitly requested)
    const forceDownload = process.argv.includes('--force-download');
    
    if (forceDownload) {
      console.log("\n🚀 Step 3: Testing with force download...");
      console.log("⚠️  This will run the Apify actor and consume credits!");
      
      try {
        const forceResponse = await axios.post(`${baseUrl}/admin/us-tariffs/sync-from-apify`, {
          forceDownload: true
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          timeout: 60000  // 60 second timeout for actor run
        });

        console.log("✅ Force download endpoint successful");
        console.log("📊 Response status:", forceResponse.status);
        console.log("📋 Response data:", JSON.stringify(forceResponse.data, null, 2));
        
      } catch (forceError) {
        console.log("❌ Force download failed:", forceError.response?.data || forceError.message);
      }
    } else {
      console.log("\nℹ️  Skipping force download test (use --force-download to test)");
    }

    console.log("\n✅ HTTP endpoint testing completed!");
    
  } catch (error) {
    console.error("💥 HTTP endpoint test failed:", error.response?.data || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error("🔌 Connection refused - is the portal-api server running on port 5001?");
      console.error("💡 Try: cd apps/portal-api && rushx start");
    }
    
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const helpRequested = args.includes('--help') || args.includes('-h');

if (helpRequested) {
  console.log(`
US Tariff HTTP Endpoints Test

Usage: node test-http-endpoints.js [options]

Options:
  --force-download    Test with force download (consumes Apify credits)
  --help, -h         Show this help message

Prerequisites:
  - portal-api server must be running on http://localhost:5001
  - Database must be accessible
  - Valid test user credentials

Examples:
  # Basic endpoint test (recommended)
  node test-http-endpoints.js
  
  # Test with force download (use sparingly)
  node test-http-endpoints.js --force-download
`);
  process.exit(0);
}

// Main execution
if (require.main === module) {
  testHttpEndpoints();
}