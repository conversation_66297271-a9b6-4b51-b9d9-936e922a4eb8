import {
  BadRequestException,
  Body,
  Controller,
  Post,
  UploadedFile,
  UseGuards,
  UseInterceptors
} from "@nestjs/common";
import { InjectQueue } from "@nestjs/bullmq";
import { FileInterceptor } from "@nestjs/platform-express";
import { ApiConsumes, ApiOperation, ApiResponse, ApiTags, ApiBody } from "@nestjs/swagger";
import { Queue } from "bullmq";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  TariffSyncHistory,
  UsTariffIngestionService
} from "nest-modules";
import { UsTariffDownloadJobData, UsTariffQueueName } from "./types/apify.types";

@ApiTags("US Tariff Administration")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("admin/us-tariffs")
export class UsTariffUploadController {
  constructor(
    private readonly ingestionService: UsTariffIngestionService,
    @InjectQueue(UsTariffQueueName.US_TARIFF_DOWNLOAD)
    private readonly usTariffDownloadQueue: Queue<UsTariffDownloadJobData>
  ) {}

  @Post("upload")
  @ApiOperation({
    summary: "Upload and process US tariff XLSX file",
    description: "Upload an HTS PGA report XLSX file and trigger ingestion of US tariff data"
  })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    description: "XLSX file containing US tariff data",
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
          description: "XLSX file (max 5MB)"
        }
      },
      required: ["file"]
    }
  })
  @ApiResponse({
    status: 200,
    description: "File uploaded and processing initiated successfully",
    type: TariffSyncHistory
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - file missing or invalid format"
  })
  @ApiResponse({
    status: 413,
    description: "File too large (max 5MB)"
  })
  @UseInterceptors(
    FileInterceptor("file", {
      limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
      },
      fileFilter: (_, file, callback) => {
        // Accept only XLSX files
        const allowedMimeTypes = [
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "application/vnd.ms-excel"
        ];

        if (allowedMimeTypes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(
            new BadRequestException(`Invalid file type. Expected XLSX file, got: ${file.mimetype}`),
            false
          );
        }
      }
    })
  )
  async uploadTariffFile(@UploadedFile() file: Express.Multer.File): Promise<TariffSyncHistory> {
    if (!file) {
      throw new BadRequestException("File is required");
    }

    if (!file.buffer || file.buffer.length === 0) {
      throw new BadRequestException("File is empty");
    }

    // TODO: Get actual user ID from request context
    // For now using a placeholder - this should be extracted from JWT token
    const userId = "system"; // Replace with actual user extraction logic

    try {
      return await this.ingestionService.ingestFromXlsx(file.buffer, userId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new BadRequestException(`Failed to process XLSX file: ${errorMessage}`);
    }
  }

  @Post("sync-from-apify")
  @ApiOperation({
    summary: "Trigger US tariff sync from Apify",
    description: "Queue a background job to download and process the latest US tariff data from Apify"
  })
  @ApiBody({
    description: "Sync options",
    schema: {
      type: "object",
      properties: {
        date: {
          type: "string",
          pattern: "^\\d{6}$",
          description: "Date in MMDDYY format (optional, defaults to today)",
          example: "070425"
        },
        forceDownload: {
          type: "boolean",
          description: "Force download even if file already exists",
          default: false
        }
      }
    },
    required: false
  })
  @ApiResponse({
    status: 200,
    description: "Sync job queued successfully",
    schema: {
      type: "object",
      properties: {
        jobId: {
          type: "string",
          description: "Background job ID"
        },
        message: {
          type: "string",
          description: "Success message"
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - invalid date format"
  })
  async syncFromApify(@Body() dto: { date?: string; forceDownload?: boolean }) {
    const { date, forceDownload = false } = dto;

    // Validate date format if provided
    if (date && !/^\d{6}$/.test(date)) {
      throw new BadRequestException("Date must be in MMDDYY format (e.g., 070425)");
    }

    try {
      // Queue the download job
      const job = await this.usTariffDownloadQueue.add(
        "us-tariff-download",
        {
          date,
          forceDownload,
          organizationId: null // TODO: Extract from request context if needed
        },
        {
          attempts: 1,
          removeOnComplete: {
            age: 3600,
            count: 1000
          },
          removeOnFail: {
            age: 24 * 3600
          }
        }
      );

      return {
        jobId: job.id,
        message: `US Tariff sync job queued for date: ${date || "today"}`
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new BadRequestException(`Failed to queue sync job: ${errorMessage}`);
    }
  }
}
