import { Processor, WorkerHost } from "@nestjs/bullmq";
import { forwardRef, Inject, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Job } from "bullmq";
import moment from "moment";
import {
  UsTariffApifyService,
  UsTariffIngestionService,
  TariffSyncHistory,
  SyncStatus,
  ActorRunStatus,
  APIFY_GET_RUN_STATUS_INTERVAL_MS,
  APIFY_GET_RUN_STATUS_MAX_ATTEMPTS,
  INITIAL_RUN_STATUSES,
  TERMINAL_RUN_STATUSES,
  TRANSITIONAL_RUN_STATUSES,
  ApifyService
} from "nest-modules";
import { UsTariffDownloadJobData, UsTariffQueueName } from "../types/apify.types";

const DEFAULT_WORKER_OPTIONS = {
  concurrency: 1
};

@Processor(
  {
    name: UsTariffQueueName.US_TARIFF_DOWNLOAD
  },
  DEFAULT_WORKER_OPTIONS
)
export class UsTariffDownloadProcessor extends WorkerHost {
  private readonly logger = new Logger(UsTariffDownloadProcessor.name);

  constructor(
    @Inject(forwardRef(() => UsTariffApifyService))
    private readonly usTariffApifyService: UsTariffApifyService,
    @Inject(forwardRef(() => UsTariffIngestionService))
    private readonly usTariffIngestionService: UsTariffIngestionService,
    @Inject(ApifyService)
    private readonly apifyService: ApifyService,
    @InjectRepository(TariffSyncHistory)
    private readonly historyRepository: Repository<TariffSyncHistory>
  ) {
    super();
  }

  async process(job: Job<UsTariffDownloadJobData>) {
    this.logger.log(`Starting US Tariff download job: ${job.id}, Data: ${JSON.stringify(job.data)}`);

    const { date, forceDownload = false, organizationId } = job.data;
    const targetDate = date || moment().format("MMDDYY");

    // Create sync history record
    const history = this.historyRepository.create({
      status: SyncStatus.RUNNING
    });
    await this.historyRepository.save(history);

    try {
      this.logger.log(`Processing US Tariff download for date: ${targetDate}, sync ID: ${history.id}`);

      // Check if file already exists and force download is not enabled
      if (!forceDownload) {
        const fileExists = await this.usTariffApifyService.tariffFileExists(targetDate);
        if (fileExists) {
          this.logger.log(`Tariff file already exists for date ${targetDate}, skipping actor run`);

          // Download and process existing file
          const fileBuffer = await this.usTariffApifyService.downloadTariffFile(targetDate);
          await this.processFile(fileBuffer, history, targetDate);
          return;
        }
      }

      // Run the Apify actor
      this.logger.log(`Running Apify actor for US Tariff download...`);
      let actorRun = await this.usTariffApifyService.runTariffDownload(targetDate, forceDownload);

      // Poll for completion
      let attempts = 0;
      while (
        attempts < APIFY_GET_RUN_STATUS_MAX_ATTEMPTS &&
        (INITIAL_RUN_STATUSES.includes(actorRun.data?.status) ||
          TRANSITIONAL_RUN_STATUSES.includes(actorRun.data?.status))
      ) {
        this.logger.log(`Actor run status: ${actorRun.data?.status}, waiting...`);
        attempts++;

        // Wait before checking status again
        await this.waitForTimeout(APIFY_GET_RUN_STATUS_INTERVAL_MS);

        // Get updated run status
        actorRun = await this.apifyService.getRun(actorRun.data?.id);

        this.logger.log(`Attempt ${attempts}, Actor run status: ${actorRun.data?.status}`);
      }

      // Check final status
      if (!TERMINAL_RUN_STATUSES.includes(actorRun.data?.status)) {
        throw new Error(`Actor run did not reach terminal status. Final status: ${actorRun.data?.status}`);
      }

      if (actorRun.data?.status !== ActorRunStatus.SUCCEEDED) {
        throw new Error(`Actor run failed with status: ${actorRun.data?.status}`);
      }

      this.logger.log(`Actor run completed successfully: ${actorRun.data?.id}`);

      // Download the file from key-value store
      this.logger.log(`Downloading tariff file from Apify store...`);
      const fileBuffer = await this.usTariffApifyService.downloadTariffFile(targetDate);

      // Process the file
      await this.processFile(fileBuffer, history, targetDate);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`US Tariff download job failed: ${errorMessage}`, errorStack);

      // Update history with failure
      Object.assign(history, {
        finishDate: new Date(),
        status: SyncStatus.FAILED,
        errorMessage
      });
      await this.historyRepository.save(history);

      throw error;
    }
  }

  /**
   * Process the downloaded XLSX file
   */
  private async processFile(
    fileBuffer: Buffer,
    history: TariffSyncHistory,
    targetDate: string
  ): Promise<void> {
    this.logger.log(`Processing tariff file for date ${targetDate}, size: ${fileBuffer.length} bytes`);

    try {
      // Use existing ingestion service to process the workbook
      const result = await this.usTariffIngestionService.ingestFromXlsx(fileBuffer, `Apify-${targetDate}`);

      this.logger.log(`File processing completed successfully for date ${targetDate}`);

      // Update history with success (ingestion service already handles its own history)
      Object.assign(history, {
        finishDate: new Date(),
        status: SyncStatus.SUCCESS
      });
      await this.historyRepository.save(history);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to process tariff file: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Wait for specified timeout
   */
  private async waitForTimeout(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
