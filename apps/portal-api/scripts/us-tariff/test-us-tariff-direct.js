#!/usr/bin/env node

const fs = require("fs");
const path = require("path");

/**
 * Direct test of US Tariff Ingestion Service
 * Tests the service directly without HTTP layer
 */

async function testDirectIngestion() {
  console.log("🧪 Testing US Tariff E2E Pipeline (Apify → Database)");
  console.log("=====================================================");

  let fileBuffer;
  let fileSource = "unknown";

  try {
    // Import required modules
    console.log("\n🔧 Setting up NestJS application...");

    // Import NestJS modules (using core-agent e2e testing pattern)
    const { NestFactory, ContextIdFactory } = require("@nestjs/core");
    const { AppModule } = require("../../dist/app.module");
    const { UsTariffIngestionService, UsTariffApifyService } = require("nest-modules");

    console.log("🚀 Creating NestJS application context...");
    const contextId = ContextIdFactory.create();
    const app = await NestFactory.createApplicationContext(AppModule);

    // Inject request context (demo organization)
    app.registerRequestByContextId(
      {
        user: {
          permission: "backoffice-admin",
          organization: {
            id: 3 // Demo organization
          }
        }
      },
      contextId
    );

    try {
      // Get both services with request context
      const apifyService = await app.resolve(UsTariffApifyService, contextId);
      const ingestionService = await app.resolve(UsTariffIngestionService, contextId);
      console.log("✅ Got Apify and ingestion services");

      console.log("\n📥 Step 1: Testing Apify download...");
      const downloadStartTime = Date.now();

      // Test downloading today's file from Apify
      const targetDate = new Date()
        .toLocaleDateString("en-US", {
          month: "2-digit",
          day: "2-digit",
          year: "2-digit"
        })
        .replace(/\//g, "");

      console.log(`🎯 Target date: ${targetDate}`);

      try {
        // First check if file exists in Apify store
        const fileExists = await apifyService.tariffFileExists(targetDate);
        console.log(`📋 File exists in Apify store: ${fileExists}`);

        if (!fileExists) {
          console.log("🚀 File not found, running Apify actor to download...");
          const actorRun = await apifyService.runTariffDownload(targetDate, false);
          console.log(`🔄 Actor run started: ${actorRun.data?.id}`);

          // Wait for actor to complete (simplified - in real processor we poll)
          console.log("⏳ Waiting 30 seconds for actor to complete...");
          await new Promise((resolve) => setTimeout(resolve, 30000));
        }

        // Download the file
        fileBuffer = await apifyService.downloadTariffFile(targetDate);
        fileSource = `Apify (${targetDate})`;

        const downloadEndTime = Date.now();
        const downloadDuration = ((downloadEndTime - downloadStartTime) / 1000).toFixed(2);

        console.log(`✅ Download completed in ${downloadDuration}s`);
        console.log(`📊 File size: ${(fileBuffer.length / 1024 / 1024).toFixed(2)} MB`);
      } catch (apifyError) {
        console.log(`⚠️ Apify download failed: ${apifyError.message}`);
        console.log("🔄 Falling back to local file if available...");

        // Fallback to local file
        const localPath = ".ai-reference/us-tariff-data/hts_pga_report_June29.xls";
        if (fs.existsSync(localPath)) {
          fileBuffer = fs.readFileSync(localPath);
          fileSource = "Local file";
          console.log(`✅ Using local file: ${localPath}`);
        } else {
          throw new Error("Both Apify download and local file failed");
        }
      }

      console.log(`\n📊 Step 2: Processing XLSX file from ${fileSource}...`);
      const ingestionStartTime = Date.now();

      // Test the ingestion
      const result = await ingestionService.ingestFromXlsx(fileBuffer, "e2e-test-user");

      const ingestionEndTime = Date.now();
      const ingestionDuration = ((ingestionEndTime - ingestionStartTime) / 1000).toFixed(2);

      console.log(`⏱️  Ingestion completed in ${ingestionDuration}s`);
      console.log("\n📋 Ingestion Result:");
      console.log(JSON.stringify(result, null, 2));

      // Check database state
      console.log("\n📊 Step 3: Verifying database state...");
      await checkDatabaseState();

      console.log("\n✅ E2E Pipeline test completed successfully!");
      console.log(`📈 Total time: ${((ingestionEndTime - downloadStartTime) / 1000).toFixed(2)}s`);
      console.log(`📊 Source: ${fileSource}`);
    } finally {
      await app.close();
    }
  } catch (error) {
    console.error("💥 Test failed:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

async function checkDatabaseState() {
  console.log("\n🗄️  Checking database state...");

  const { spawn } = require("child_process");

  return new Promise((resolve, reject) => {
    const psql = spawn(
      "psql",
      [
        "-U",
        "postgres",
        "-h",
        "localhost",
        "-d",
        "claro_dev",
        "-c",
        `
        SELECT COUNT(*) as tariff_count FROM us_tariff;
        SELECT COUNT(*) as history_count FROM tariff_sync_history;
        SELECT status, "syncDate", "finishDate" FROM tariff_sync_history ORDER BY "syncDate" DESC LIMIT 3;
        SELECT "hsCode", description FROM us_tariff LIMIT 5;
      `
      ],
      {
        env: { ...process.env, PGPASSWORD: "superuser" }
      }
    );

    let output = "";
    let error = "";

    psql.stdout.on("data", (data) => {
      output += data.toString();
    });

    psql.stderr.on("data", (data) => {
      error += data.toString();
    });

    psql.on("close", (code) => {
      if (code === 0) {
        console.log("📊 Database state:");
        console.log(output);
        resolve();
      } else {
        console.error("❌ Database check failed:", error);
        reject(new Error(error));
      }
    });
  });
}

// Main execution
if (require.main === module) {
  testDirectIngestion();
}
