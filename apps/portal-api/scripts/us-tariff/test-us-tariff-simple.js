#!/usr/bin/env node

const fs = require("fs");

/**
 * Simple test to verify US Tariff ingestion service can be imported and works
 */

const EXCEL_FILE_PATH = ".ai-reference/us-tariff-data/hts_pga_report_June29.xls";

async function testSimpleIngestion() {
  console.log("🧪 Testing US Tariff Ingestion Service (Simple Test)");
  console.log("==================================================");

  try {
    // Check if Excel file exists
    if (!fs.existsSync(EXCEL_FILE_PATH)) {
      throw new Error(`Excel file not found at: ${EXCEL_FILE_PATH}`);
    }

    const fileStats = fs.statSync(EXCEL_FILE_PATH);
    console.log(`📁 File: ${EXCEL_FILE_PATH}`);
    console.log(`📊 Size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);

    // Read file buffer
    const fileBuffer = fs.readFileSync(EXCEL_FILE_PATH);
    console.log(`✅ File loaded into buffer (${fileBuffer.length} bytes)`);

    // Import the ingestion service
    console.log("\n🔧 Importing US Tariff modules...");

    try {
      const { UsTariffIngestionService } = require("./libraries/nest-modules/dist/us-tariff");
      console.log("✅ UsTariffIngestionService imported successfully");

      const { UsTariff } = require("./libraries/nest-modules/dist/entities");
      console.log("✅ UsTariff entity imported successfully");

      const { TariffSyncHistory } = require("./libraries/nest-modules/dist/entities");
      console.log("✅ TariffSyncHistory entity imported successfully");

      console.log("\n📊 Module import test successful!");
      console.log("🎯 The US Tariff ingestion service is properly built and available");
      console.log("🎯 To test with database, the portal-api server needs to be running");
    } catch (importError) {
      console.error("❌ Failed to import modules:", importError.message);
      throw importError;
    }

    console.log("\n✅ Simple ingestion test completed successfully!");
  } catch (error) {
    console.error("💥 Test failed:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Main execution
if (require.main === module) {
  testSimpleIngestion();
}
