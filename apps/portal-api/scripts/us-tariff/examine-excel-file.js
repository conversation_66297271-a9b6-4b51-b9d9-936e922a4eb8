#!/usr/bin/env node

const fs = require("fs");
// Use xlsx from nest-modules dependencies
process.chdir(__dirname);
const modulesDirPath = require
  .resolve("./libraries/nest-modules/package.json")
  .replace("/package.json", "/node_modules");
const XLSX = require(`${modulesDirPath}/xlsx`);

/**
 * Examine the structure of the US Tariff Excel file
 */

const EXCEL_FILE_PATH = ".ai-reference/us-tariff-data/hts_pga_report_June29.xls";

function examineExcelFile() {
  console.log("🔍 Examining US Tariff Excel File");
  console.log("=================================");

  try {
    // Check if Excel file exists
    if (!fs.existsSync(EXCEL_FILE_PATH)) {
      throw new Error(`Excel file not found at: ${EXCEL_FILE_PATH}`);
    }

    const fileStats = fs.statSync(EXCEL_FILE_PATH);
    console.log(`📁 File: ${EXCEL_FILE_PATH}`);
    console.log(`📊 Size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);

    // Read and parse the Excel file
    console.log("\n📖 Reading Excel file...");
    const workbook = XLSX.readFile(EXCEL_FILE_PATH);

    console.log(`📋 Worksheets found: ${workbook.SheetNames.length}`);
    workbook.SheetNames.forEach((name, index) => {
      console.log(`  ${index + 1}. ${name}`);
    });

    // Examine the first worksheet
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];

    console.log(`\n🔍 Examining worksheet: "${firstSheetName}"`);

    // Get the range of the worksheet
    const range = XLSX.utils.decode_range(worksheet["!ref"]);
    console.log(`📐 Range: ${worksheet["!ref"]} (${range.e.r + 1} rows, ${range.e.c + 1} columns)`);

    // Convert to array of arrays to examine structure
    const rawRows = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      raw: false,
      defval: ""
    });

    console.log(`\n📊 Total rows: ${rawRows.length}`);

    // Show first few rows to understand structure
    console.log("\n📋 First 5 rows:");
    rawRows.slice(0, 5).forEach((row, index) => {
      console.log(
        `Row ${index + 1}:`,
        row
          .slice(0, 10)
          .map((cell) =>
            typeof cell === "string" && cell.length > 20 ? cell.substring(0, 20) + "..." : cell
          )
      );
    });

    // Try to identify header row
    console.log("\n🔍 Looking for header patterns...");
    for (let i = 0; i < Math.min(10, rawRows.length); i++) {
      const row = rawRows[i];
      const hasExpectedHeaders = row.some(
        (cell) =>
          typeof cell === "string" &&
          (cell.toLowerCase().includes("tariff") ||
            cell.toLowerCase().includes("description") ||
            cell.toLowerCase().includes("effective") ||
            cell.toLowerCase().includes("pga"))
      );

      if (hasExpectedHeaders) {
        console.log(`🎯 Potential header row found at row ${i + 1}:`);
        console.log(row.slice(0, 15));
        break;
      }
    }

    // Look for data patterns
    console.log("\n🔍 Analyzing data patterns...");
    let dataRowsFound = 0;
    let sampleDataRows = [];

    for (let i = 1; i < Math.min(100, rawRows.length); i++) {
      const row = rawRows[i];

      // Look for rows that might contain HTS codes (10-digit numbers with dots)
      const potentialHtsCode = row[0];
      if (
        typeof potentialHtsCode === "string" &&
        (potentialHtsCode.match(/^\d{4}\.\d{2}\.\d{2}\.\d{2}$/) || potentialHtsCode.match(/^\d{10}$/))
      ) {
        dataRowsFound++;
        if (sampleDataRows.length < 3) {
          sampleDataRows.push({ rowIndex: i + 1, data: row.slice(0, 10) });
        }
      }
    }

    console.log(`📊 Found ${dataRowsFound} potential data rows with HTS codes`);

    if (sampleDataRows.length > 0) {
      console.log("\n📋 Sample data rows:");
      sampleDataRows.forEach((sample) => {
        console.log(
          `Row ${sample.rowIndex}:`,
          sample.data.map((cell) =>
            typeof cell === "string" && cell.length > 25 ? cell.substring(0, 25) + "..." : cell
          )
        );
      });
    }

    // Check for empty rows
    const emptyRows = rawRows.filter((row) => row.every((cell) => !cell || cell.toString().trim() === ""));
    console.log(`\n📊 Empty rows: ${emptyRows.length}`);

    console.log("\n✅ Excel file examination completed!");
  } catch (error) {
    console.error("💥 Examination failed:", error.message);
    console.error(error.stack);
  }
}

// Main execution
if (require.main === module) {
  examineExcelFile();
}
