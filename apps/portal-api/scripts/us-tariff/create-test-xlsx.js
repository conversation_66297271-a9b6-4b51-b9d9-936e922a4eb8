#!/usr/bin/env node

/**
 * Create a simple test XLSX file for US Tariff testing
 * This creates a minimal valid HTS PGA report structure
 */

// Use xlsx from nest-modules dependencies
const modulesDirPath = require
  .resolve("../../../libraries/nest-modules/package.json")
  .replace("/package.json", "/node_modules");
const XLSX = require(`${modulesDirPath}/xlsx`);

function createTestXlsx() {
  console.log("📋 Creating test XLSX file for US Tariff testing...");

  // Create test data with proper structure
  const testData = [
    // Header rows (first 3 rows are skipped by ingestion logic)
    ["HTS PGA Report", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""],
    ["Generated for Testing", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""],
    [
      "Date: " + new Date().toISOString().split("T")[0],
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      ""
    ],

    // Data header row (row 4, index 3)
    [
      "Tariff Number",
      "Description",
      "Effective Date",
      "Expiration Date",
      "Census Qty 1",
      "Census Qty 2",
      "Census Qty 3",
      "AD Duty",
      "CV Duty",
      "PGA1",
      "PGA2",
      "PGA3",
      "PGA4",
      "PGA5",
      "PGA6",
      "PGA7",
      "PGA8",
      "PGA9",
      "PGA10"
    ],

    // Sample data rows
    [
      "1234567890",
      "Test Product - Electronic Components",
      "2024-01-01",
      "2025-12-31",
      "NO",
      "NO",
      "NO",
      "N",
      "N",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      ""
    ],
    [
      "2345678901",
      "Test Product - Agricultural Items",
      "2024-01-01",
      "2025-12-31",
      "KG",
      "NO",
      "NO",
      "Y",
      "N",
      "FD1",
      "AM1",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      ""
    ],
    [
      "3456789012",
      "Test Product - Textiles",
      "2024-01-01",
      "2025-12-31",
      "M2",
      "KG",
      "NO",
      "N",
      "Y",
      "TX1",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      ""
    ],
    [
      "4567890123",
      "Test Product - Machinery",
      "2024-01-01",
      "2025-12-31",
      "NO",
      "NO",
      "NO",
      "N",
      "N",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      "",
      ""
    ],
    [
      "5678901234",
      "Test Product - Chemicals",
      "2024-01-01",
      "2025-12-31",
      "KG",
      "L",
      "NO",
      "Y",
      "Y",
      "EP1",
      "FD1",
      "AM2",
      "",
      "",
      "",
      "",
      "",
      "",
      ""
    ]
  ];

  // Create workbook and worksheet
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(testData);

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(wb, ws, "HTS PGA Report");

  // Write file
  const filePath = ".ai-reference/us-tariff-data/hts_pga_report_June29.xls";
  XLSX.writeFile(wb, filePath);

  console.log(`✅ Test XLSX file created: ${filePath}`);
  console.log(`📊 Contains ${testData.length - 4} test tariff records`);

  return filePath;
}

// Main execution
if (require.main === module) {
  createTestXlsx();
}

module.exports = { createTestXlsx };
